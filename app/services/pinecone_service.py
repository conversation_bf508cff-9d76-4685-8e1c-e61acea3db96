import config
from pinecone import Pinecone


class PineconeService:

    def __init__(self):
        self.api_key = config.PINECONE_API_KEY_ADMIN
        self.environment = config.PINECONE_ENVIRONMENT
        self.pinecone = Pinecone(api_key=self.api_key, environment=self.environment)

    def get_namespaces(self, index_name: str) -> list[str]:
        """
        Return all namespaces for a given Pinecone index.

        Args:
            index_name (str): The name of the Pinecone index.
            api_key (str): Pinecone API key.

        Returns:
            list[str]: List of namespaces in the index.
        """
        pc = self.pinecone
        index = pc.Index(index_name)
        stats = index.describe_index_stats()
        return list(stats.get("namespaces", {}).keys())