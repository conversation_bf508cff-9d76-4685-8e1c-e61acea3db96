../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/__init__.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/_elffile.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/_manylinux.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/_musllinux.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/_parser.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/_structures.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/_tokenizer.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/licenses/__init__.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/licenses/_spdx.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/markers.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/metadata.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/requirements.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/specifiers.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/tags.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/utils.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/ws_ai/bookgpt_v2/venv/lib/python3.9/site-packages/packaging/version.cpython-39.pyc,,
packaging-24.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
packaging-24.2.dist-info/LICENSE,sha256=ytHvW9NA1z4HS6YU0m996spceUDD2MNIUuZcSQlobEg,197
packaging-24.2.dist-info/LICENSE.APACHE,sha256=DVQuDIgE45qn836wDaWnYhSdxoLXgpRRKH4RuTjpRZQ,10174
packaging-24.2.dist-info/LICENSE.BSD,sha256=tw5-m3QvHMb5SLNMFqo5_-zpQZY2S8iP8NIYDwAo-sU,1344
packaging-24.2.dist-info/METADATA,sha256=ohH86s6k5mIfQxY2TS0LcSfADeOFa4BiCC-bxZV-pNs,3204
packaging-24.2.dist-info/RECORD,,
packaging-24.2.dist-info/WHEEL,sha256=CpUCUxeHQbRN5UGRQHYRJorO5Af-Qy_fHMctcQ8DSGI,82
packaging/__init__.py,sha256=dk4Ta_vmdVJxYHDcfyhvQNw8V3PgSBomKNXqg-D2JDY,494
packaging/_elffile.py,sha256=cflAQAkE25tzhYmq_aCi72QfbT_tn891tPzfpbeHOwE,3306
packaging/_manylinux.py,sha256=vl5OCoz4kx80H5rwXKeXWjl9WNISGmr4ZgTpTP9lU9c,9612
packaging/_musllinux.py,sha256=p9ZqNYiOItGee8KcZFeHF_YcdhVwGHdK6r-8lgixvGQ,2694
packaging/_parser.py,sha256=s_TvTvDNK0NrM2QB3VKThdWFM4Nc0P6JnkObkl3MjpM,10236
packaging/_structures.py,sha256=q3eVNmbWJGG_S0Dit_S3Ao8qQqz_5PYTXFAKBZe5yr4,1431
packaging/_tokenizer.py,sha256=J6v5H7Jzvb-g81xp_2QACKwO7LxHQA6ikryMU7zXwN8,5273
packaging/licenses/__init__.py,sha256=1x5M1nEYjcgwEbLt0dXwz2ukjr18DiCzC0sraQqJ-Ww,5715
packaging/licenses/_spdx.py,sha256=oAm1ztPFwlsmCKe7lAAsv_OIOfS1cWDu9bNBkeu-2ns,48398
packaging/markers.py,sha256=c89TNzB7ZdGYhkovm6PYmqGyHxXlYVaLW591PHUNKD8,10561
packaging/metadata.py,sha256=YJibM7GYe4re8-0a3OlXmGS-XDgTEoO4tlBt2q25Bng,34762
packaging/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
packaging/requirements.py,sha256=gYyRSAdbrIyKDY66ugIDUQjRMvxkH2ALioTmX3tnL6o,2947
packaging/specifiers.py,sha256=GG1wPNMcL0fMJO68vF53wKMdwnfehDcaI-r9NpTfilA,40074
packaging/tags.py,sha256=CFqrJzAzc2XNGexerH__T-Y5Iwq7WbsYXsiLERLWxY0,21014
packaging/utils.py,sha256=0F3Hh9OFuRgrhTgGZUl5K22Fv1YP2tZl1z_2gO6kJiA,5050
packaging/version.py,sha256=olfyuk_DPbflNkJ4wBWetXQ17c74x3DB501degUv7DY,16676
