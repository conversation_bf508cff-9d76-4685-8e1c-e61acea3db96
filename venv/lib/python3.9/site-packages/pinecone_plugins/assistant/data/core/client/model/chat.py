"""
    Pinecone Assistant Data Plane API

    Pinecone Assistant Engine is a context engine to store and retrieve relevant knowledge from millions of documents at scale. This API supports interactions with assistants.  # noqa: E501

    The version of the OpenAPI document: 2025-10
    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401
import sys  # noqa: F401

from pinecone_plugins.assistant.data.core.client.model_utils import (  # noqa: F401
    PineconeApiTypeError,
    ModelComposed,
    ModelNormal,
    ModelSimple,
    cached_property,
    change_keys_js_to_python,
    convert_js_args_to_python_args,
    date,
    datetime,
    file_type,
    none_type,
    validate_get_composed_info,
)
from ..model_utils import OpenApiModel
from pinecone_plugins.assistant.data.core.client.exceptions import PineconeApiAttributeError


def lazy_import():
    from pinecone_plugins.assistant.data.core.client.model.context_options_model import ContextOptionsModel
    from pinecone_plugins.assistant.data.core.client.model.message_model import MessageModel
    globals()['ContextOptionsModel'] = ContextOptionsModel
    globals()['MessageModel'] = MessageModel


class Chat(ModelNormal):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.

    Attributes:
      allowed_values (dict): The key is the tuple path to the attribute
          and the for var_name this is (var_name,). The value is a dict
          with a capitalized key describing the allowed value and an allowed
          value. These dicts store the allowed enum values.
      attribute_map (dict): The key is attribute name
          and the value is json key in definition.
      discriminator_value_class_map (dict): A dict to go from the discriminator
          variable value to the discriminator class name.
      validations (dict): The key is the tuple path to the attribute
          and the for var_name this is (var_name,). The value is a dict
          that stores validations for max_length, min_length, max_items,
          min_items, exclusive_maximum, inclusive_maximum, exclusive_minimum,
          inclusive_minimum, and regex.
      additional_properties_type (tuple): A tuple of classes accepted
          as additional properties values.
    """

    allowed_values = {
        ('model',): {
            'GPT-4O': "gpt-4o",
            'GPT-4.1': "gpt-4.1",
            'O4-MINI': "o4-mini",
            'CLAUDE-3-5-SONNET': "claude-3-5-sonnet",
            'CLAUDE-3-7-SONNET': "claude-3-7-sonnet",
            'GEMINI-2.5-PRO': "gemini-2.5-pro",
        },
    }

    validations = {
    }

    @cached_property
    def additional_properties_type():
        """
        This must be a method because a model may have properties that are
        of type self, this must run after the class is loaded
        """
        lazy_import()
        return (bool, date, datetime, dict, float, int, list, str, none_type,)  # noqa: E501

    _nullable = False

    @cached_property
    def openapi_types():
        """
        This must be a method because a model may have properties that are
        of type self, this must run after the class is loaded

        Returns
            openapi_types (dict): The key is attribute name
                and the value is attribute type.
        """
        lazy_import()
        return {
            'messages': ([MessageModel],),  # noqa: E501
            'stream': (bool,),  # noqa: E501
            'model': (str,),  # noqa: E501
            'temperature': (float,),  # noqa: E501
            'filter': ({str: (bool, date, datetime, dict, float, int, list, str, none_type)},),  # noqa: E501
            'json_response': (bool,),  # noqa: E501
            'include_highlights': (bool,),  # noqa: E501
            'context_options': (ContextOptionsModel,),  # noqa: E501
        }

    @cached_property
    def discriminator():
        return None


    attribute_map = {
        'messages': 'messages',  # noqa: E501
        'stream': 'stream',  # noqa: E501
        'model': 'model',  # noqa: E501
        'temperature': 'temperature',  # noqa: E501
        'filter': 'filter',  # noqa: E501
        'json_response': 'json_response',  # noqa: E501
        'include_highlights': 'include_highlights',  # noqa: E501
        'context_options': 'context_options',  # noqa: E501
    }

    read_only_vars = {
    }

    _composed_schemas = {}

    @classmethod
    @convert_js_args_to_python_args
    def _from_openapi_data(cls, messages, *args, **kwargs):  # noqa: E501
        """Chat - a model defined in OpenAPI

        Args:
            messages ([MessageModel]):

        Keyword Args:
            _check_type (bool): if True, values for parameters in openapi_types
                                will be type checked and a TypeError will be
                                raised if the wrong type is input.
                                Defaults to True
            _path_to_item (tuple/list): This is a list of keys or values to
                                drill down to the model in received_data
                                when deserializing a response
            _spec_property_naming (bool): True if the variable names in the input data
                                are serialized names, as specified in the OpenAPI document.
                                False if the variable names in the input data
                                are pythonic names, e.g. snake case (default)
            _configuration (Configuration): the instance to use when
                                deserializing a file_type parameter.
                                If passed, type conversion is attempted
                                If omitted no type conversion is done.
            _visited_composed_classes (tuple): This stores a tuple of
                                classes that we have traveled through so that
                                if we see that class again we will not use its
                                discriminator again.
                                When traveling through a discriminator, the
                                composed schema that is
                                is traveled through is added to this set.
                                For example if Animal has a discriminator
                                petType and we pass in "Dog", and the class Dog
                                allOf includes Animal, we move through Animal
                                once using the discriminator, and pick Dog.
                                Then in Dog, we will make an instance of the
                                Animal class but this time we won't travel
                                through its discriminator because we passed in
                                _visited_composed_classes = (Animal,)
            stream (bool): If false, the assistant will return a single JSON response. If true, the assistant will return a stream of responses.. [optional] if omitted the server will use the default value of False  # noqa: E501
            model (str): The large language model to use for answer generation. [optional] if omitted the server will use the default value of "gpt-4o"  # noqa: E501
            temperature (float): Controls the randomness of the model's output: lower values make responses more deterministic, while higher values increase creativity and variability. If the model does not support a temperature parameter, the parameter will be ignored.. [optional] if omitted the server will use the default value of 0.0  # noqa: E501
            filter ({str: (bool, date, datetime, dict, float, int, list, str, none_type)}): Optionally filter which documents can be retrieved using the following metadata fields.. [optional]  # noqa: E501
            json_response (bool): If true, the assistant will be instructed to return a JSON response. Cannot be used with streaming.. [optional] if omitted the server will use the default value of False  # noqa: E501
            include_highlights (bool): If true, the assistant will be instructed to return highlights from the referenced documents that support its response.. [optional] if omitted the server will use the default value of False  # noqa: E501
            context_options (ContextOptionsModel): [optional]  # noqa: E501
        """

        _check_type = kwargs.pop('_check_type', True)
        _spec_property_naming = kwargs.pop('_spec_property_naming', False)
        _path_to_item = kwargs.pop('_path_to_item', ())
        _configuration = kwargs.pop('_configuration', None)
        _visited_composed_classes = kwargs.pop('_visited_composed_classes', ())

        self = super(OpenApiModel, cls).__new__(cls)

        if args:
            raise PineconeApiTypeError(
                "Invalid positional arguments=%s passed to %s. Remove those invalid positional arguments." % (
                    args,
                    self.__class__.__name__,
                ),
                path_to_item=_path_to_item,
                valid_classes=(self.__class__,),
            )

        self._data_store = {}
        self._check_type = _check_type
        self._spec_property_naming = _spec_property_naming
        self._path_to_item = _path_to_item
        self._configuration = _configuration
        self._visited_composed_classes = _visited_composed_classes + (self.__class__,)

        self.messages = messages
        for var_name, var_value in kwargs.items():
            if var_name not in self.attribute_map and \
                        self._configuration is not None and \
                        self._configuration.discard_unknown_keys and \
                        self.additional_properties_type is None:
                # discard variable.
                continue
            setattr(self, var_name, var_value)
        return self

    required_properties = set([
        '_data_store',
        '_check_type',
        '_spec_property_naming',
        '_path_to_item',
        '_configuration',
        '_visited_composed_classes',
    ])

    @convert_js_args_to_python_args
    def __init__(self, messages, *args, **kwargs):  # noqa: E501
        """Chat - a model defined in OpenAPI

        Args:
            messages ([MessageModel]):

        Keyword Args:
            _check_type (bool): if True, values for parameters in openapi_types
                                will be type checked and a TypeError will be
                                raised if the wrong type is input.
                                Defaults to True
            _path_to_item (tuple/list): This is a list of keys or values to
                                drill down to the model in received_data
                                when deserializing a response
            _spec_property_naming (bool): True if the variable names in the input data
                                are serialized names, as specified in the OpenAPI document.
                                False if the variable names in the input data
                                are pythonic names, e.g. snake case (default)
            _configuration (Configuration): the instance to use when
                                deserializing a file_type parameter.
                                If passed, type conversion is attempted
                                If omitted no type conversion is done.
            _visited_composed_classes (tuple): This stores a tuple of
                                classes that we have traveled through so that
                                if we see that class again we will not use its
                                discriminator again.
                                When traveling through a discriminator, the
                                composed schema that is
                                is traveled through is added to this set.
                                For example if Animal has a discriminator
                                petType and we pass in "Dog", and the class Dog
                                allOf includes Animal, we move through Animal
                                once using the discriminator, and pick Dog.
                                Then in Dog, we will make an instance of the
                                Animal class but this time we won't travel
                                through its discriminator because we passed in
                                _visited_composed_classes = (Animal,)
            stream (bool): If false, the assistant will return a single JSON response. If true, the assistant will return a stream of responses.. [optional] if omitted the server will use the default value of False  # noqa: E501
            model (str): The large language model to use for answer generation. [optional] if omitted the server will use the default value of "gpt-4o"  # noqa: E501
            temperature (float): Controls the randomness of the model's output: lower values make responses more deterministic, while higher values increase creativity and variability. If the model does not support a temperature parameter, the parameter will be ignored.. [optional] if omitted the server will use the default value of 0.0  # noqa: E501
            filter ({str: (bool, date, datetime, dict, float, int, list, str, none_type)}): Optionally filter which documents can be retrieved using the following metadata fields.. [optional]  # noqa: E501
            json_response (bool): If true, the assistant will be instructed to return a JSON response. Cannot be used with streaming.. [optional] if omitted the server will use the default value of False  # noqa: E501
            include_highlights (bool): If true, the assistant will be instructed to return highlights from the referenced documents that support its response.. [optional] if omitted the server will use the default value of False  # noqa: E501
            context_options (ContextOptionsModel): [optional]  # noqa: E501
        """

        _check_type = kwargs.pop('_check_type', True)
        _spec_property_naming = kwargs.pop('_spec_property_naming', False)
        _path_to_item = kwargs.pop('_path_to_item', ())
        _configuration = kwargs.pop('_configuration', None)
        _visited_composed_classes = kwargs.pop('_visited_composed_classes', ())

        if args:
            raise PineconeApiTypeError(
                "Invalid positional arguments=%s passed to %s. Remove those invalid positional arguments." % (
                    args,
                    self.__class__.__name__,
                ),
                path_to_item=_path_to_item,
                valid_classes=(self.__class__,),
            )

        self._data_store = {}
        self._check_type = _check_type
        self._spec_property_naming = _spec_property_naming
        self._path_to_item = _path_to_item
        self._configuration = _configuration
        self._visited_composed_classes = _visited_composed_classes + (self.__class__,)

        self.messages = messages
        for var_name, var_value in kwargs.items():
            if var_name not in self.attribute_map and \
                        self._configuration is not None and \
                        self._configuration.discard_unknown_keys and \
                        self.additional_properties_type is None:
                # discard variable.
                continue
            setattr(self, var_name, var_value)
            if var_name in self.read_only_vars:
                raise PineconeApiAttributeError(f"`{var_name}` is a read-only attribute. Use `from_openapi_data` to instantiate "
                                     f"class with read only attributes.")
