"""
    Pinecone Assistant Data Plane API

    Pinecone Assistant Engine is a context engine to store and retrieve relevant knowledge from millions of documents at scale. This API supports interactions with assistants.  # noqa: E501

    The version of the OpenAPI document: 2025-10
    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401
import sys  # noqa: F401

from pinecone_plugins.assistant.data.core.client.api_client import ApiClient, Endpoint as _Endpoint
from pinecone_plugins.assistant.data.core.client.model_utils import (  # noqa: F401
    check_allowed_values,
    check_validations,
    date,
    datetime,
    file_type,
    none_type,
    validate_and_convert_types
)
from pinecone_plugins.assistant.data.core.client.model.assistant_file_model import AssistantFileModel
from pinecone_plugins.assistant.data.core.client.model.chat import Chat
from pinecone_plugins.assistant.data.core.client.model.chat_completion_model import ChatCompletionModel
from pinecone_plugins.assistant.data.core.client.model.chat_model import ChatModel
from pinecone_plugins.assistant.data.core.client.model.context_model import ContextModel
from pinecone_plugins.assistant.data.core.client.model.context_request import ContextRequest
from pinecone_plugins.assistant.data.core.client.model.error_response import ErrorResponse
from pinecone_plugins.assistant.data.core.client.model.inline_response200 import InlineResponse200
from pinecone_plugins.assistant.data.core.client.model.search_completions import SearchCompletions


class ManageAssistantsApi(object):
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

        def __chat_assistant(
            self,
            assistant_name,
            chat,
            **kwargs
        ):
            """Chat with an assistant  # noqa: E501

            Chat with an assistant and get back citations in structured form.   This is the recommended way to chat with an assistant, as it offers more functionality and control over the assistant's responses and references than the OpenAI-compatible chat interface.  For guidance and examples, see [Chat with an assistant](https://docs.pinecone.io/guides/assistant/chat-with-assistant).  # noqa: E501
            This method makes a synchronous HTTP request by default. To make an
            asynchronous HTTP request, please pass async_req=True

            >>> thread = api.chat_assistant(assistant_name, chat, async_req=True)
            >>> result = thread.get()

            Args:
                assistant_name (str): The name of the assistant to be described.
                chat (Chat): The desired configuration to chat an assistant.

            Keyword Args:
                _return_http_data_only (bool): response data without head status
                    code and headers. Default is True.
                _preload_content (bool): if False, the urllib3.HTTPResponse object
                    will be returned without reading/decoding response data.
                    Default is True.
                _request_timeout (int/float/tuple): timeout setting for this request. If
                    one number provided, it will be total request timeout. It can also
                    be a pair (tuple) of (connection, read) timeouts.
                    Default is None.
                _check_input_type (bool): specifies if type checking
                    should be done one the data sent to the server.
                    Default is True.
                _check_return_type (bool): specifies if type checking
                    should be done one the data received from the server.
                    Default is True.
                _host_index (int/None): specifies the index of the server
                    that we want to use.
                    Default is read from the configuration.
                async_req (bool): execute request asynchronously

            Returns:
                ChatModel
                    If the method is called asynchronously, returns the request
                    thread.
            """
            kwargs['async_req'] = kwargs.get(
                'async_req', False
            )
            kwargs['_return_http_data_only'] = kwargs.get(
                '_return_http_data_only', True
            )
            kwargs['_preload_content'] = kwargs.get(
                '_preload_content', True
            )
            kwargs['_request_timeout'] = kwargs.get(
                '_request_timeout', None
            )
            kwargs['_check_input_type'] = kwargs.get(
                '_check_input_type', True
            )
            kwargs['_check_return_type'] = kwargs.get(
                '_check_return_type', True
            )
            kwargs['_host_index'] = kwargs.get('_host_index')
            kwargs['assistant_name'] = \
                assistant_name
            kwargs['chat'] = \
                chat
            return self.call_with_http_info(**kwargs)

        self.chat_assistant = _Endpoint(
            settings={
                'response_type': (ChatModel,),
                'auth': [
                    'ApiKeyAuth'
                ],
                'endpoint_path': '/chat/{assistant_name}',
                'operation_id': 'chat_assistant',
                'http_method': 'POST',
                'servers': None,
            },
            params_map={
                'all': [
                    'assistant_name',
                    'chat',
                ],
                'required': [
                    'assistant_name',
                    'chat',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'assistant_name':
                        (str,),
                    'chat':
                        (Chat,),
                },
                'attribute_map': {
                    'assistant_name': 'assistant_name',
                },
                'location_map': {
                    'assistant_name': 'path',
                    'chat': 'body',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json'
                ],
                'content_type': [
                    'application/json'
                ]
            },
            api_client=api_client,
            callable=__chat_assistant
        )

        def __chat_completion_assistant(
            self,
            assistant_name,
            search_completions,
            **kwargs
        ):
            """Chat through an OpenAI-compatible interface  # noqa: E501

            Chat with an assistant. This endpoint is based on the OpenAI Chat Completion API, a commonly used and adopted API.   It is useful if you need inline citations or OpenAI-compatible responses, but has limited functionality compared to the standard chat interface.  For guidance and examples, see [Chat with an assistant](https://docs.pinecone.io/guides/assistant/chat-with-assistant).  # noqa: E501
            This method makes a synchronous HTTP request by default. To make an
            asynchronous HTTP request, please pass async_req=True

            >>> thread = api.chat_completion_assistant(assistant_name, search_completions, async_req=True)
            >>> result = thread.get()

            Args:
                assistant_name (str): The name of the assistant to be described.
                search_completions (SearchCompletions): The desired configuration to chat an assistant.

            Keyword Args:
                _return_http_data_only (bool): response data without head status
                    code and headers. Default is True.
                _preload_content (bool): if False, the urllib3.HTTPResponse object
                    will be returned without reading/decoding response data.
                    Default is True.
                _request_timeout (int/float/tuple): timeout setting for this request. If
                    one number provided, it will be total request timeout. It can also
                    be a pair (tuple) of (connection, read) timeouts.
                    Default is None.
                _check_input_type (bool): specifies if type checking
                    should be done one the data sent to the server.
                    Default is True.
                _check_return_type (bool): specifies if type checking
                    should be done one the data received from the server.
                    Default is True.
                _host_index (int/None): specifies the index of the server
                    that we want to use.
                    Default is read from the configuration.
                async_req (bool): execute request asynchronously

            Returns:
                ChatCompletionModel
                    If the method is called asynchronously, returns the request
                    thread.
            """
            kwargs['async_req'] = kwargs.get(
                'async_req', False
            )
            kwargs['_return_http_data_only'] = kwargs.get(
                '_return_http_data_only', True
            )
            kwargs['_preload_content'] = kwargs.get(
                '_preload_content', True
            )
            kwargs['_request_timeout'] = kwargs.get(
                '_request_timeout', None
            )
            kwargs['_check_input_type'] = kwargs.get(
                '_check_input_type', True
            )
            kwargs['_check_return_type'] = kwargs.get(
                '_check_return_type', True
            )
            kwargs['_host_index'] = kwargs.get('_host_index')
            kwargs['assistant_name'] = \
                assistant_name
            kwargs['search_completions'] = \
                search_completions
            return self.call_with_http_info(**kwargs)

        self.chat_completion_assistant = _Endpoint(
            settings={
                'response_type': (ChatCompletionModel,),
                'auth': [
                    'ApiKeyAuth'
                ],
                'endpoint_path': '/chat/{assistant_name}/chat/completions',
                'operation_id': 'chat_completion_assistant',
                'http_method': 'POST',
                'servers': None,
            },
            params_map={
                'all': [
                    'assistant_name',
                    'search_completions',
                ],
                'required': [
                    'assistant_name',
                    'search_completions',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'assistant_name':
                        (str,),
                    'search_completions':
                        (SearchCompletions,),
                },
                'attribute_map': {
                    'assistant_name': 'assistant_name',
                },
                'location_map': {
                    'assistant_name': 'path',
                    'search_completions': 'body',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json',
                    'text/event-stream'
                ],
                'content_type': [
                    'application/json'
                ]
            },
            api_client=api_client,
            callable=__chat_completion_assistant
        )

        def __context_assistant(
            self,
            assistant_name,
            context_request,
            **kwargs
        ):
            """Retrieve context from an assistant  # noqa: E501

            Retrieve context snippets from an assistant to use as part of RAG or any agentic flow.  For guidance and examples, see [Retrieve context snippets](https://docs.pinecone.io/guides/assistant/retrieve-context-snippets).  # noqa: E501
            This method makes a synchronous HTTP request by default. To make an
            asynchronous HTTP request, please pass async_req=True

            >>> thread = api.context_assistant(assistant_name, context_request, async_req=True)
            >>> result = thread.get()

            Args:
                assistant_name (str): The name of the assistant to be described.
                context_request (ContextRequest): The desired configuration to retrieve context from an assistant.

            Keyword Args:
                _return_http_data_only (bool): response data without head status
                    code and headers. Default is True.
                _preload_content (bool): if False, the urllib3.HTTPResponse object
                    will be returned without reading/decoding response data.
                    Default is True.
                _request_timeout (int/float/tuple): timeout setting for this request. If
                    one number provided, it will be total request timeout. It can also
                    be a pair (tuple) of (connection, read) timeouts.
                    Default is None.
                _check_input_type (bool): specifies if type checking
                    should be done one the data sent to the server.
                    Default is True.
                _check_return_type (bool): specifies if type checking
                    should be done one the data received from the server.
                    Default is True.
                _host_index (int/None): specifies the index of the server
                    that we want to use.
                    Default is read from the configuration.
                async_req (bool): execute request asynchronously

            Returns:
                ContextModel
                    If the method is called asynchronously, returns the request
                    thread.
            """
            kwargs['async_req'] = kwargs.get(
                'async_req', False
            )
            kwargs['_return_http_data_only'] = kwargs.get(
                '_return_http_data_only', True
            )
            kwargs['_preload_content'] = kwargs.get(
                '_preload_content', True
            )
            kwargs['_request_timeout'] = kwargs.get(
                '_request_timeout', None
            )
            kwargs['_check_input_type'] = kwargs.get(
                '_check_input_type', True
            )
            kwargs['_check_return_type'] = kwargs.get(
                '_check_return_type', True
            )
            kwargs['_host_index'] = kwargs.get('_host_index')
            kwargs['assistant_name'] = \
                assistant_name
            kwargs['context_request'] = \
                context_request
            return self.call_with_http_info(**kwargs)

        self.context_assistant = _Endpoint(
            settings={
                'response_type': (ContextModel,),
                'auth': [
                    'ApiKeyAuth'
                ],
                'endpoint_path': '/chat/{assistant_name}/context',
                'operation_id': 'context_assistant',
                'http_method': 'POST',
                'servers': None,
            },
            params_map={
                'all': [
                    'assistant_name',
                    'context_request',
                ],
                'required': [
                    'assistant_name',
                    'context_request',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'assistant_name':
                        (str,),
                    'context_request':
                        (ContextRequest,),
                },
                'attribute_map': {
                    'assistant_name': 'assistant_name',
                },
                'location_map': {
                    'assistant_name': 'path',
                    'context_request': 'body',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json'
                ],
                'content_type': [
                    'application/json'
                ]
            },
            api_client=api_client,
            callable=__context_assistant
        )

        def __delete_file(
            self,
            assistant_name,
            assistant_file_id,
            **kwargs
        ):
            """Delete an uploaded file  # noqa: E501

            Delete an uploaded file from an assistant.  For guidance and examples, see [Manage files](https://docs.pinecone.io/guides/assistant/manage-files#delete-a-file).  # noqa: E501
            This method makes a synchronous HTTP request by default. To make an
            asynchronous HTTP request, please pass async_req=True

            >>> thread = api.delete_file(assistant_name, assistant_file_id, async_req=True)
            >>> result = thread.get()

            Args:
                assistant_name (str): The name of the assistant to upload files to.
                assistant_file_id (str): The uuid of the file to be described.

            Keyword Args:
                _return_http_data_only (bool): response data without head status
                    code and headers. Default is True.
                _preload_content (bool): if False, the urllib3.HTTPResponse object
                    will be returned without reading/decoding response data.
                    Default is True.
                _request_timeout (int/float/tuple): timeout setting for this request. If
                    one number provided, it will be total request timeout. It can also
                    be a pair (tuple) of (connection, read) timeouts.
                    Default is None.
                _check_input_type (bool): specifies if type checking
                    should be done one the data sent to the server.
                    Default is True.
                _check_return_type (bool): specifies if type checking
                    should be done one the data received from the server.
                    Default is True.
                _host_index (int/None): specifies the index of the server
                    that we want to use.
                    Default is read from the configuration.
                async_req (bool): execute request asynchronously

            Returns:
                None
                    If the method is called asynchronously, returns the request
                    thread.
            """
            kwargs['async_req'] = kwargs.get(
                'async_req', False
            )
            kwargs['_return_http_data_only'] = kwargs.get(
                '_return_http_data_only', True
            )
            kwargs['_preload_content'] = kwargs.get(
                '_preload_content', True
            )
            kwargs['_request_timeout'] = kwargs.get(
                '_request_timeout', None
            )
            kwargs['_check_input_type'] = kwargs.get(
                '_check_input_type', True
            )
            kwargs['_check_return_type'] = kwargs.get(
                '_check_return_type', True
            )
            kwargs['_host_index'] = kwargs.get('_host_index')
            kwargs['assistant_name'] = \
                assistant_name
            kwargs['assistant_file_id'] = \
                assistant_file_id
            return self.call_with_http_info(**kwargs)

        self.delete_file = _Endpoint(
            settings={
                'response_type': None,
                'auth': [
                    'ApiKeyAuth'
                ],
                'endpoint_path': '/files/{assistant_name}/{assistant_file_id}',
                'operation_id': 'delete_file',
                'http_method': 'DELETE',
                'servers': None,
            },
            params_map={
                'all': [
                    'assistant_name',
                    'assistant_file_id',
                ],
                'required': [
                    'assistant_name',
                    'assistant_file_id',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'assistant_name':
                        (str,),
                    'assistant_file_id':
                        (str,),
                },
                'attribute_map': {
                    'assistant_name': 'assistant_name',
                    'assistant_file_id': 'assistant_file_id',
                },
                'location_map': {
                    'assistant_name': 'path',
                    'assistant_file_id': 'path',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json'
                ],
                'content_type': [],
            },
            api_client=api_client,
            callable=__delete_file
        )

        def __describe_file(
            self,
            assistant_name,
            assistant_file_id,
            **kwargs
        ):
            """Describe a file upload  # noqa: E501

            Get the status and metadata of a file uploaded to an assistant.  For guidance and examples, see [Manage files](https://docs.pinecone.io/guides/assistant/manage-files#get-the-status-of-a-file).  # noqa: E501
            This method makes a synchronous HTTP request by default. To make an
            asynchronous HTTP request, please pass async_req=True

            >>> thread = api.describe_file(assistant_name, assistant_file_id, async_req=True)
            >>> result = thread.get()

            Args:
                assistant_name (str): The name of the assistant to upload files to.
                assistant_file_id (str): The uuid of the file to be described.

            Keyword Args:
                include_url (str): Include the signed URL of the file in the response. [optional]
                _return_http_data_only (bool): response data without head status
                    code and headers. Default is True.
                _preload_content (bool): if False, the urllib3.HTTPResponse object
                    will be returned without reading/decoding response data.
                    Default is True.
                _request_timeout (int/float/tuple): timeout setting for this request. If
                    one number provided, it will be total request timeout. It can also
                    be a pair (tuple) of (connection, read) timeouts.
                    Default is None.
                _check_input_type (bool): specifies if type checking
                    should be done one the data sent to the server.
                    Default is True.
                _check_return_type (bool): specifies if type checking
                    should be done one the data received from the server.
                    Default is True.
                _host_index (int/None): specifies the index of the server
                    that we want to use.
                    Default is read from the configuration.
                async_req (bool): execute request asynchronously

            Returns:
                AssistantFileModel
                    If the method is called asynchronously, returns the request
                    thread.
            """
            kwargs['async_req'] = kwargs.get(
                'async_req', False
            )
            kwargs['_return_http_data_only'] = kwargs.get(
                '_return_http_data_only', True
            )
            kwargs['_preload_content'] = kwargs.get(
                '_preload_content', True
            )
            kwargs['_request_timeout'] = kwargs.get(
                '_request_timeout', None
            )
            kwargs['_check_input_type'] = kwargs.get(
                '_check_input_type', True
            )
            kwargs['_check_return_type'] = kwargs.get(
                '_check_return_type', True
            )
            kwargs['_host_index'] = kwargs.get('_host_index')
            kwargs['assistant_name'] = \
                assistant_name
            kwargs['assistant_file_id'] = \
                assistant_file_id
            return self.call_with_http_info(**kwargs)

        self.describe_file = _Endpoint(
            settings={
                'response_type': (AssistantFileModel,),
                'auth': [
                    'ApiKeyAuth'
                ],
                'endpoint_path': '/files/{assistant_name}/{assistant_file_id}',
                'operation_id': 'describe_file',
                'http_method': 'GET',
                'servers': None,
            },
            params_map={
                'all': [
                    'assistant_name',
                    'assistant_file_id',
                    'include_url',
                ],
                'required': [
                    'assistant_name',
                    'assistant_file_id',
                ],
                'nullable': [
                ],
                'enum': [
                    'include_url',
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                    ('include_url',): {

                        "TRUE": "true",
                        "FALSE": "false"
                    },
                },
                'openapi_types': {
                    'assistant_name':
                        (str,),
                    'assistant_file_id':
                        (str,),
                    'include_url':
                        (str,),
                },
                'attribute_map': {
                    'assistant_name': 'assistant_name',
                    'assistant_file_id': 'assistant_file_id',
                    'include_url': 'include_url',
                },
                'location_map': {
                    'assistant_name': 'path',
                    'assistant_file_id': 'path',
                    'include_url': 'query',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json'
                ],
                'content_type': [],
            },
            api_client=api_client,
            callable=__describe_file
        )

        def __list_files(
            self,
            assistant_name,
            **kwargs
        ):
            """List Files  # noqa: E501

            List all files in an assistant, with an option to filter files with metadata.  For guidance and examples, see [Manage files](https://docs.pinecone.io/guides/assistant/manage-files#list-files-in-an-assistant).  # noqa: E501
            This method makes a synchronous HTTP request by default. To make an
            asynchronous HTTP request, please pass async_req=True

            >>> thread = api.list_files(assistant_name, async_req=True)
            >>> result = thread.get()

            Args:
                assistant_name (str): The name of the assistant to list files for.

            Keyword Args:
                filter (str): Optional JSON-encoded metadata filter for files. [optional]
                _return_http_data_only (bool): response data without head status
                    code and headers. Default is True.
                _preload_content (bool): if False, the urllib3.HTTPResponse object
                    will be returned without reading/decoding response data.
                    Default is True.
                _request_timeout (int/float/tuple): timeout setting for this request. If
                    one number provided, it will be total request timeout. It can also
                    be a pair (tuple) of (connection, read) timeouts.
                    Default is None.
                _check_input_type (bool): specifies if type checking
                    should be done one the data sent to the server.
                    Default is True.
                _check_return_type (bool): specifies if type checking
                    should be done one the data received from the server.
                    Default is True.
                _host_index (int/None): specifies the index of the server
                    that we want to use.
                    Default is read from the configuration.
                async_req (bool): execute request asynchronously

            Returns:
                InlineResponse200
                    If the method is called asynchronously, returns the request
                    thread.
            """
            kwargs['async_req'] = kwargs.get(
                'async_req', False
            )
            kwargs['_return_http_data_only'] = kwargs.get(
                '_return_http_data_only', True
            )
            kwargs['_preload_content'] = kwargs.get(
                '_preload_content', True
            )
            kwargs['_request_timeout'] = kwargs.get(
                '_request_timeout', None
            )
            kwargs['_check_input_type'] = kwargs.get(
                '_check_input_type', True
            )
            kwargs['_check_return_type'] = kwargs.get(
                '_check_return_type', True
            )
            kwargs['_host_index'] = kwargs.get('_host_index')
            kwargs['assistant_name'] = \
                assistant_name
            return self.call_with_http_info(**kwargs)

        self.list_files = _Endpoint(
            settings={
                'response_type': (InlineResponse200,),
                'auth': [
                    'ApiKeyAuth'
                ],
                'endpoint_path': '/files/{assistant_name}',
                'operation_id': 'list_files',
                'http_method': 'GET',
                'servers': None,
            },
            params_map={
                'all': [
                    'assistant_name',
                    'filter',
                ],
                'required': [
                    'assistant_name',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'assistant_name':
                        (str,),
                    'filter':
                        (str,),
                },
                'attribute_map': {
                    'assistant_name': 'assistant_name',
                    'filter': 'filter',
                },
                'location_map': {
                    'assistant_name': 'path',
                    'filter': 'query',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json'
                ],
                'content_type': [],
            },
            api_client=api_client,
            callable=__list_files
        )

        def __upload_file(
            self,
            assistant_name,
            file,
            **kwargs
        ):
            """Upload file to assistant  # noqa: E501

            Upload a file to the specified assistant.  For guidance and examples, see [Manage files](https://docs.pinecone.io/guides/assistant/manage-files#upload-a-local-file).  # noqa: E501
            This method makes a synchronous HTTP request by default. To make an
            asynchronous HTTP request, please pass async_req=True

            >>> thread = api.upload_file(assistant_name, file, async_req=True)
            >>> result = thread.get()

            Args:
                assistant_name (str): The name of the assistant to upload files to.
                file (file_type): The file to upload.

            Keyword Args:
                metadata (str): Optional JSON-encoded metadata for files. [optional]
                multimodal (str): Optional flag to opt in to multimodal file processing (PDFs only). Can be either `true` or `false`. Default is `false`. [optional]
                _return_http_data_only (bool): response data without head status
                    code and headers. Default is True.
                _preload_content (bool): if False, the urllib3.HTTPResponse object
                    will be returned without reading/decoding response data.
                    Default is True.
                _request_timeout (int/float/tuple): timeout setting for this request. If
                    one number provided, it will be total request timeout. It can also
                    be a pair (tuple) of (connection, read) timeouts.
                    Default is None.
                _check_input_type (bool): specifies if type checking
                    should be done one the data sent to the server.
                    Default is True.
                _check_return_type (bool): specifies if type checking
                    should be done one the data received from the server.
                    Default is True.
                _host_index (int/None): specifies the index of the server
                    that we want to use.
                    Default is read from the configuration.
                async_req (bool): execute request asynchronously

            Returns:
                AssistantFileModel
                    If the method is called asynchronously, returns the request
                    thread.
            """
            kwargs['async_req'] = kwargs.get(
                'async_req', False
            )
            kwargs['_return_http_data_only'] = kwargs.get(
                '_return_http_data_only', True
            )
            kwargs['_preload_content'] = kwargs.get(
                '_preload_content', True
            )
            kwargs['_request_timeout'] = kwargs.get(
                '_request_timeout', None
            )
            kwargs['_check_input_type'] = kwargs.get(
                '_check_input_type', True
            )
            kwargs['_check_return_type'] = kwargs.get(
                '_check_return_type', True
            )
            kwargs['_host_index'] = kwargs.get('_host_index')
            kwargs['assistant_name'] = \
                assistant_name
            kwargs['file'] = \
                file
            return self.call_with_http_info(**kwargs)

        self.upload_file = _Endpoint(
            settings={
                'response_type': (AssistantFileModel,),
                'auth': [
                    'ApiKeyAuth'
                ],
                'endpoint_path': '/files/{assistant_name}',
                'operation_id': 'upload_file',
                'http_method': 'POST',
                'servers': None,
            },
            params_map={
                'all': [
                    'assistant_name',
                    'file',
                    'metadata',
                    'multimodal',
                ],
                'required': [
                    'assistant_name',
                    'file',
                ],
                'nullable': [
                ],
                'enum': [
                ],
                'validation': [
                ]
            },
            root_map={
                'validations': {
                },
                'allowed_values': {
                },
                'openapi_types': {
                    'assistant_name':
                        (str,),
                    'file':
                        (file_type,),
                    'metadata':
                        (str,),
                    'multimodal':
                        (str,),
                },
                'attribute_map': {
                    'assistant_name': 'assistant_name',
                    'file': 'file',
                    'metadata': 'metadata',
                    'multimodal': 'multimodal',
                },
                'location_map': {
                    'assistant_name': 'path',
                    'file': 'form',
                    'metadata': 'query',
                    'multimodal': 'query',
                },
                'collection_format_map': {
                }
            },
            headers_map={
                'accept': [
                    'application/json'
                ],
                'content_type': [
                    'multipart/form-data'
                ]
            },
            api_client=api_client,
            callable=__upload_file
        )
